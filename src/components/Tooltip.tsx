import { expand } from 'inline-style-expand-shorthand';
import { StatefulTooltip as BaseStatefulTooltip, StatefulTooltipProps } from 'baseui/tooltip';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';

export default function StatefulTooltip(props: StatefulTooltipProps) {
  const [, theme] = useStyletron();

  return (
    <BaseStatefulTooltip
      showArrow
      popoverMargin={4}
      {...props}
      overrides={{
        Arrow: {
          style: {
            width: '5.66px',
            height: '5.66px',
          },
        },
        Inner: {
          style: {
            fontWeight: 400,
            fontSize: '12px',
            lineHeight: '16px',
            color: '#fff',
            backgroundColor: theme.colors.gray1000,
            ...expand({
              padding: '8px 16px',
              borderRadius: '5px',
              margin: '-1px', // for the offset of arrow
            }),
          },
        },
        ...props.overrides,
      }}
    >
      {props.children}
    </BaseStatefulTooltip>
  );
}
