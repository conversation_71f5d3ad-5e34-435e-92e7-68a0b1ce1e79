import { axiosCluster } from '@/lib/network';
import { Return } from '@/lib/type';
import { WorkspaceT } from '@/pages/workgroup/type';
import { useQuery } from 'react-query';

export interface BuiltInEndpoint {
  description: string;
  parameters: Record<string, any>;
}
export type BuiltInEndpoints = Record<string, BuiltInEndpoint>;

export const useBuiltinEndpoints = (wp: WorkspaceT | undefined, options?: { enabled?: boolean }) => {
  return useQuery(
    ['builtinEndpoints', wp?.workspace_id],
    async () => {
      const res = await axiosCluster.get<Return<BuiltInEndpoints>>(`/api/restpp/endpoints`, {
        params: { builtin: true },
      });
      return res.data.results!;
    },
    {
      enabled: options?.enabled !== undefined ? (!!wp && options.enabled) : !!wp,
    }
  );
};
