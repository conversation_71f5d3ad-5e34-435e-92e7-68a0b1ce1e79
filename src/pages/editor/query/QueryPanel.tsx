import useCopyClipboard from 'react-use-clipboard';
import JSONbig from 'json-bigint';
import { ID_TOKEN_KEY, useWorkspaceContext } from '@/contexts/workspaceContext';
import DeleteQueryModal from '@/pages/editor/query/DeleteQueryModal';
import { CopyIcon } from '@/pages/home/<USER>';
import { cmOptions } from '@/pages/workgroup/tab/restPP/type';
import { FileStore } from '@/utils/graphEditor/data';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Button } from '@tigergraph/app-ui-lib/button';
import { Input } from '@tigergraph/app-ui-lib/input';
import {
  canDropQuery,
  canEditQuery,
  canInstallQuery,
  canReadQuery,
  QueryMetaLogic,
  QueryParam,
} from '@tigergraph/tools-models';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { MdCode, MdDeleteOutline, MdDownload, MdEdit } from 'react-icons/md';
import { showToast } from '@/components/styledToasterContainer';
import ReactCodeMirror from '@uiw/react-codemirror';
import { StreamLanguage } from '@codemirror/language';
import { json } from '@codemirror/legacy-modes/mode/javascript';
import { nanoid } from 'nanoid';
import {
  HTTPConfig,
  genCurlText,
  genJSText,
  genPyText,
  getInterpretQueryCode,
  getQueryDefalutPayloadByParams,
  getURLSearchParamsForQuery,
  isDistributedQuery,
} from '@/pages/editor/query/util';
import SnippetsCopier from '@/pages/editor/query/SnippetsCopier';
import { codeCheckReq } from '@/pages/editor/query/codeCheck';
import toast from 'react-hot-toast';
import useEditorTheme from '@/pages/editor/useEditorTheme';
import QueryParamPopover from '@/pages/workgroup/tab/restPP/QueryParamPopover';
import { compare } from 'compare-versions';
import { Command, QueryCommand } from '@/pages/editor/result/CommandExecutor';
import { useEditorContext } from '@/contexts/graphEditorContext';

interface QueryPanelProps {
  handleRunCmd: (cmd: string | Command) => void;
  handleEditQuery: (name: string, content: string, graphName: string) => void;
  setQueriesFolder: React.Dispatch<React.SetStateAction<FileStore>>;
  mapIdToQuery: Map<string, FileStore>;
  selectedQuery: FileStore;
  isCommandRunning: boolean;
}

const QueryPanel = forwardRef(
  (
    { handleRunCmd, handleEditQuery, mapIdToQuery, setQueriesFolder, selectedQuery, isCommandRunning }: QueryPanelProps,
    ref
  ) => {
    const [css, theme] = useStyletron();
    const editorTheme = useEditorTheme({ background: theme.colors['input.background'] });
    const { memoryLimit, timeLimit, profile } = useEditorContext();

    const query = selectedQuery;
    const queryInfo = query.queryInfo!;
    const graphName = query.graphName!;

    const { currentWorkspace, dbUser, currentGraph, setCurrentGraph } = useWorkspaceContext();

    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [showSnippets, setShowSnippets] = useState(false);

    const deleteQuery = () => {
      const cmdText = `USE GRAPH ${graphName}\nDROP QUERY ${query.name}`;
      handleRunCmd(cmdText);
    };

    // request url is workspace host + /api/restpp/query/{graphName}/{queryName}
    const queryURL = `https://${currentWorkspace?.nginx_host}/api/restpp/query/${graphName}/${query.name}`;
    const queryParams: QueryParam[] = useMemo(() => {
      const params = query.originQueryInfo?.endpoint?.query?.[graphName]?.[query.name]?.['GET/POST']?.parameters || {};
      return QueryMetaLogic.convertGSQLParameters(params);
    }, [query, graphName]);

    const [urlCopied, setUrlCopied] = useCopyClipboard(queryURL, {
      successDuration: 1000,
    });
    useEffect(() => {
      if (urlCopied) {
        showToast({
          kind: 'positive',
          message: 'Request URL copied successfully.',
        });
      }
    }, [urlCopied]);

    const [queryPayload, setQueryPayload] = useState<string>('');
    useEffect(() => {
      const defaultPayload = getQueryDefalutPayloadByParams(queryInfo.params);
      setQueryPayload(defaultPayload);
    }, [graphName, query.name, queryInfo]);

    const runQuery = () => {
      let payload: object = {};
      try {
        payload = JSONbig.parse(queryPayload);
      } catch (err: any) {
        showToast({
          kind: 'negative',
          message: `Invalid JSON payload: ${err.message}`,
        });
        return;
      }

      let cmd: QueryCommand = {
        id: nanoid(),
        type: 'Query',
        queryName: query.name,
        graph: graphName,
        workspace_id: currentWorkspace?.workspace_id!,
        URL: `/api/restpp/query/${graphName}/${query.name}`,
        payload,
        memoryLimit,
        timeLimit,
        // only profile installed queries
        profile:
          profile && query.queryInfo?.installed && selectedQuery?.queryInfo?.installMode !== 'UDF' ? true : false,
      };
      if (!queryInfo.installed) {
        const urlParams = getURLSearchParamsForQuery(graphName, queryInfo.params, payload);
        cmd.URL = compare(currentWorkspace?.tg_version || '', '4.0.x', '>')
          ? `/api/gsql-server/gsql/v1/queries/interpret?${urlParams}`
          : `/api/gsql-server/interpreted_query?${urlParams}`;
        cmd.payload = getInterpretQueryCode(queryInfo);
      }
      cmd.URL = `https://${currentWorkspace?.nginx_host}${cmd.URL}`;
      handleRunCmd(cmd);
      if (currentGraph !== graphName) {
        setCurrentGraph(graphName);
      }
    };
    useImperativeHandle(ref, () => ({
      runQuery,
    }));

    const canEdit =
      dbUser && canEditQuery(dbUser.getUesrInfo(), graphName, queryInfo.queryName, currentWorkspace!.tg_version);
    const canView =
      dbUser && canReadQuery(dbUser.getUesrInfo(), graphName, queryInfo.queryName, currentWorkspace!.tg_version);
    const operations = [
      {
        label: !canEdit && canView ? 'View' : 'Edit',
        icon: <MdEdit />,
        handleFn: () => {
          handleEditQuery(query.name, query.content, graphName);
        },
        disabled: !canEdit && !canView,
      },
      {
        label: 'Install',
        icon: <MdDownload />,
        handleFn: async () => {
          const codeCheckPromise = codeCheckReq(
            { code: queryInfo.originalCode, graph: graphName },
            {
              baseURL: `https://${currentWorkspace?.nginx_host}`,
              version: currentWorkspace?.tg_version,
              headers: {
                Authorization: `Bearer ${sessionStorage.getItem(ID_TOKEN_KEY)}`,
              },
            }
          );
          toast.promise(
            codeCheckPromise,
            {
              loading: 'Checking code errors...',
              success: 'Code checked successfully',
              error: 'Failed to check code errors',
            },
            {
              success: {
                style: {
                  display: 'none',
                },
              },
            }
          );
          const codeCheckRes = await codeCheckPromise;
          if (codeCheckRes.results?.errors.length) {
            showToast({
              kind: 'negative',
              message: `The query ${query.name} has error: ${
                codeCheckRes.results.errors[0].msg || ''
              }. Please fix it first.`,
            });
            return;
          }

          queryInfo.installing = true;
          setQueriesFolder((folder) => ({ ...folder }));

          // Install query with -SINGLE flag if it is not a distributed query
          // to improve the performance and enable query profiling
          const cmdText = `USE GRAPH ${graphName}\nINSTALL QUERY ${
            isDistributedQuery(query.content) ? '' : '-SINGLE '
          }${query.name}`;
          handleRunCmd(cmdText);
        },
        disabled:
          !dbUser ||
          !canInstallQuery(dbUser.getUesrInfo(), graphName, queryInfo.queryName, currentWorkspace!.tg_version) ||
          queryInfo.installing ||
          queryInfo.installed ||
          isCommandRunning,
      },
      {
        label: 'Delete',
        icon: <MdDeleteOutline />,
        handleFn: () => {
          setShowDeleteModal(true);
        },
        disabled:
          !dbUser ||
          !canDropQuery(dbUser.getUesrInfo(), graphName, queryInfo.queryName, currentWorkspace!.tg_version) ||
          queryInfo.installing,
      },
    ];

    const snippets = useMemo(() => {
      let data: object = {};
      try {
        data = JSON.parse(queryPayload);
      } catch (error) {
        //
      }
      const config: HTTPConfig = {
        url: queryURL,
        method: 'POST',
        headers: {
          Authorization: `Bearer ${sessionStorage.getItem(ID_TOKEN_KEY)}`,
          Accept: 'application/json, text/plain, */*',
          ContentType: 'application/json',
        },
        data,
      };
      const cURL = genCurlText(config);
      const JavaScript = genJSText(config);
      const Python = genPyText(config);
      return { cURL, JavaScript, Python };
    }, [queryPayload, queryURL]);

    return (
      <>
        <div className={css({ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' })}>
          <div className={css({ display: 'flex', gap: '8px' })}>
            {operations.map((op) => (
              <Button
                kind={op.label === 'Delete' ? 'destructive' : 'secondary'}
                size="compact"
                key={op.label}
                startEnhancer={op.icon}
                disabled={op.disabled}
                onClick={op.handleFn}
              >
                {op.label}
              </Button>
            ))}
          </div>
          <Button
            kind="secondary"
            size="compact"
            key={'code snippet'}
            startEnhancer={<MdCode />}
            onClick={() => setShowSnippets(true)}
          >
            Code Snippet
          </Button>
        </div>

        {/* <div className={css({ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' })}>
          <div className={css({ ...theme.typography.Label, color: '#2C3237', fontWeight: 500 })}>Query Content</div>
        </div>
        <div className={css({ border: '1px solid #DDDDDE', borderRadius: '2px' })}>
          <ReactCodeMirror
            value={queryInfo.code}
            basicSetup={cmOptions}
            width={'100%'}
            height={'300px'}
            extensions={[StreamLanguage.define(gsql)]}
          />
        </div> */}

        <div className={css({ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' })}>
          <div className={css({ ...theme.typography.Label, color: theme.colors['input.text'], fontWeight: 500 })}>
            Request URL
          </div>
        </div>
        <div className={css({ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '16px' })}>
          <div className={css({ flexGrow: 1 })}>
            <Input disabled={true} value={queryURL} />
          </div>
          <Button size="compact" kind="text" shape="square" onClick={setUrlCopied}>
            <CopyIcon />
          </Button>
        </div>

        <div className={css({ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' })}>
          <div className={css({ ...theme.typography.Label, color: theme.colors['input.text'], fontWeight: 500 })}>
            JSON Payload
          </div>
          {queryParams.length > 0 && <QueryParamPopover queryParams={queryParams} />}
        </div>
        <div
          className={css({ border: `1px solid ${theme.colors['input.border']}`, borderRadius: '2px' })}
          onKeyDown={(e) => e.stopPropagation()}
        >
          <ReactCodeMirror
            value={queryPayload}
            onChange={(value) => setQueryPayload(value)}
            basicSetup={cmOptions}
            height="300px"
            extensions={import.meta.env.VITEST ? [] : [StreamLanguage.define(json)]}
            theme={editorTheme}
          />
        </div>

        <DeleteQueryModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => {
            deleteQuery();
            setShowDeleteModal(false);
          }}
          queryName={query.name}
        />
        <SnippetsCopier snippets={snippets} isOpen={showSnippets} onClose={() => setShowSnippets(false)} />
      </>
    );
  }
);

QueryPanel.displayName = 'QueryPanel';

export default QueryPanel;
