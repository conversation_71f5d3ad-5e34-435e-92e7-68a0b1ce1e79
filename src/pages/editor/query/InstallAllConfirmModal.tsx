import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader } from '@tigergraph/app-ui-lib/modal';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Checkbox } from '@tigergraph/app-ui-lib/checkbox';
import { useState, useEffect, useMemo } from 'react';
import { MdClose } from 'react-icons/md';
import { GsqlQueryMeta } from '@tigergraph/tools-models';

export interface QueryWithCodeCheck extends GsqlQueryMeta {
  hasErrors: boolean;
  codeCheckCompleted: boolean;
}

export interface InstallAllConfirmModalProps {
  showModal: boolean;
  queries: QueryWithCodeCheck[];
  isCodeChecking: boolean;
  onClose: () => void;
  onConfirm: (selectedQueries: QueryWithCodeCheck[]) => void;
  graphName: string;
}

export function InstallAllConfirmModal({
  showModal,
  queries,
  isCodeChecking,
  onClose,
  onConfirm,
  graphN<PERSON>,
}: InstallAllConfirmModalProps) {
  const [css, theme] = useStyletron();
  const [selectedQueries, setSelectedQueries] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Filter queries that can be installed (no errors)
  const installableQueries = useMemo(() => queries.filter(q => !q.hasErrors), [queries]);
  const queriesWithErrors = useMemo(() => queries.filter(q => q.hasErrors), [queries]);

  useEffect(() => {
    // Initialize with all installable queries selected
    const installableQueryNames = new Set(installableQueries.map(q => q.name));
    setSelectedQueries(installableQueryNames);
    setSelectAll(installableQueryNames.size === installableQueries.length && installableQueries.length > 0);
  }, [installableQueries]);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedQueries(new Set(installableQueries.map(q => q.name)));
    } else {
      setSelectedQueries(new Set());
    }
  };

  const handleQuerySelect = (queryName: string, checked: boolean) => {
    const newSelected = new Set(selectedQueries);
    if (checked) {
      newSelected.add(queryName);
    } else {
      newSelected.delete(queryName);
    }
    setSelectedQueries(newSelected);
    setSelectAll(newSelected.size === installableQueries.length && installableQueries.length > 0);
  };

  const handleConfirm = () => {
    const selectedQueryObjects = installableQueries.filter(q => selectedQueries.has(q.name));
    onConfirm(selectedQueryObjects);
  };

  if (isCodeChecking) {
    return (
      <Modal isOpen={showModal} onClose={onClose}>
        <ModalHeader>Checking Code...</ModalHeader>
        <ModalBody>
          <div className={css({ textAlign: 'center', padding: '20px' })}>
            Running code check on queries...
          </div>
        </ModalBody>
      </Modal>
    );
  }

  return (
    <Modal isOpen={showModal} onClose={onClose}>
      <ModalHeader>
        <div className={css({ display: 'flex', justifyContent: 'space-between', alignItems: 'center' })}>
          Install All
        </div>
      </ModalHeader>
      <ModalBody>
        <div className={css({ marginBottom: '16px', fontWeight: 600 })}>
          {graphName}
        </div>

        {queriesWithErrors.length > 0 && (
          <div className={css({
            marginBottom: '16px',
            padding: '12px',
            backgroundColor: theme.colors['background.negative'],
            borderRadius: '4px'
          })}>
            <div className={css({ fontWeight: 600, marginBottom: '8px', color: theme.colors['text.negative'] })}>
              Queries with errors (cannot be installed):
            </div>
            {queriesWithErrors.map((query) => (
              <div key={query.name} className={css({ marginLeft: '16px', color: theme.colors['text.negative'] })}>
                • {query.name}
              </div>
            ))}
          </div>
        )}

        {installableQueries.length > 0 && (
          <div>
            <div className={css({ display: 'flex', alignItems: 'center', marginBottom: '12px' })}>
              <Checkbox
                checked={selectAll}
                onChange={(e) => handleSelectAll(e.target.checked)}
              />
              <span className={css({ marginLeft: '8px', fontWeight: 600 })}>All</span>
            </div>

            <div className={css({
              display: 'grid',
              gridTemplateColumns: '1fr 1fr',
              gap: '8px',
              maxHeight: '300px',
              overflowY: 'auto'
            })}>
              {installableQueries.map((query) => (
                <div key={query.name} className={css({ display: 'flex', alignItems: 'center' })}>
                  <Checkbox
                    checked={selectedQueries.has(query.name)}
                    onChange={(e) => handleQuerySelect(query.name, e.target.checked)}
                  />
                  <span className={css({ marginLeft: '8px' })}>{query.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {installableQueries.length === 0 && queriesWithErrors.length > 0 && (
          <div className={css({ textAlign: 'center', padding: '20px', color: theme.colors['text.secondary'] })}>
            No queries available for installation. Please fix the errors first.
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <ModalButton kind="secondary" onClick={onClose}>
          Cancel
        </ModalButton>
        <ModalButton
          disabled={selectedQueries.size === 0}
          onClick={handleConfirm}
          kind="primary"
        >
          Install
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
}
