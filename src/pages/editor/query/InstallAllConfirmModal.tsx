import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from '@tigergraph/app-ui-lib/modal';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Checkbox } from '@tigergraph/app-ui-lib/checkbox';
import { useState, useEffect, useMemo } from 'react';
import { GsqlQueryMeta } from '@tigergraph/tools-models';

export interface QueryWithCodeCheck extends GsqlQueryMeta {
  hasErrors: boolean;
  codeCheckCompleted: boolean;
}

export interface InstallAllConfirmModalProps {
  showModal: boolean;
  queries: QueryWithCodeCheck[];
  onClose: () => void;
  onConfirm: (selectedQueries: QueryWithCodeCheck[]) => void;
  graphName: string;
}

export function InstallAllConfirmModal({
  showModal,
  queries,
  onClose,
  onConfirm,
  graphName,
}: InstallAllConfirmModalProps) {
  const [css, theme] = useSty<PERSON>ron();
  const [selectedQueries, setSelectedQueries] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Filter queries that can be installed (no errors)
  const installableQueries = useMemo(() => queries.filter((q) => !q.hasErrors), [queries]);
  const queriesWithErrors = useMemo(() => queries.filter((q) => q.hasErrors), [queries]);

  useEffect(() => {
    // Initialize with all installable queries selected
    const installableQueryNames = new Set(installableQueries.map((q) => q.name));
    setSelectedQueries(installableQueryNames);
    setSelectAll(installableQueryNames.size === installableQueries.length && installableQueries.length > 0);
  }, [installableQueries]);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedQueries(new Set(installableQueries.map((q) => q.name)));
    } else {
      setSelectedQueries(new Set());
    }
  };

  const handleQuerySelect = (queryName: string, checked: boolean) => {
    const newSelected = new Set(selectedQueries);
    if (checked) {
      newSelected.add(queryName);
    } else {
      newSelected.delete(queryName);
    }
    setSelectedQueries(newSelected);
    setSelectAll(newSelected.size === installableQueries.length && installableQueries.length > 0);
  };

  const handleConfirm = () => {
    const selectedQueryObjects = installableQueries.filter((q) => selectedQueries.has(q.name));
    onConfirm(selectedQueryObjects);
  };

  return (
    <Modal isOpen={showModal} onClose={onClose}>
      <ModalHeader>
        <div className={css({ display: 'flex', justifyContent: 'space-between', alignItems: 'center' })}>
          Install All
        </div>
      </ModalHeader>
      <ModalBody>
        <div className={css({ display: 'flex', flexDirection: 'column', gap: '16px' })}>
          {installableQueries.length > 0 && (
            <div className={css({ padding: '8px 4px 12px 16px', backgroundColor: theme.colors['background.secondary'] })}>
              <div className={css({ marginBottom: '8px', fontWeight: 700, color: theme.colors['text.primary'] })}>{graphName}</div>
              <div className={css({ color: theme.colors['checkbox.text'] })}>
                <div className={css({ display: 'flex', alignItems: 'center', marginBottom: '8px' })}>
                  <Checkbox checked={selectAll} onChange={(e) => handleSelectAll(e.currentTarget.checked)} />
                  <span className={css({ marginLeft: '8px' })}>All</span>
                </div>

                <div
                  className={css({
                    display: 'grid',
                    gridTemplateColumns: '1fr 1fr',
                    gap: '8px',
                    maxHeight: '300px',
                    overflowY: 'auto',
                  })}
                >
                  {installableQueries.map((query) => (
                    <div key={query.name} className={css({ display: 'flex', alignItems: 'center' })}>
                      <Checkbox
                        checked={selectedQueries.has(query.name)}
                        onChange={(e) => handleQuerySelect(query.name, e.currentTarget.checked)}
                      />
                      <span className={css({ marginLeft: '8px' })}>{query.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {installableQueries.length === 0 && queriesWithErrors.length > 0 && (
            <div className={css({ color: theme.colors['text.secondary'] })}>
              No queries available for installation. Please fix the errors first.
            </div>
          )}

          {queriesWithErrors.length > 0 && <div className={css({ color: theme.colors['text.secondary'] })}>The following queries have errors and cannot be installed: {queriesWithErrors.map((query) => query.name).join(', ')}.</div>}
        </div>
      </ModalBody>
      <ModalFooter>
        <ModalButton kind="secondary" onClick={onClose}>
          Cancel
        </ModalButton>
        <ModalButton disabled={selectedQueries.size === 0} onClick={handleConfirm} kind="primary">
          Install
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
}
