import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header } from '@tigergraph/app-ui-lib/modal';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { ParagraphSmall } from 'baseui/typography';
import { useState } from 'react';
import { QueryMeta } from '@tigergraph/tools-models';

export interface InstallAllConfirmModalProps {
  showModal: boolean;
  queriesHasError: QueryMeta[];
  queriesToInstall: QueryMeta[];
  onClose: () => void;
  onConfirm: () => void;
}

export function InstallAllConfirmModal({
  showModal,
  queriesHasError,
  queriesToInstall,
  onClose,
  onConfirm,
}: InstallAllConfirmModalProps) {
  const [css, theme] = useStyletron();
  const [inputValue, setInputValue] = useState('');

  return (
    <Modal isOpen={showModal} onClose={onClose}>
      <ModalHeader>Install queries</ModalHeader>
      <ModalBody>
        {queriesHasError.length > 0 && (
          <ParagraphSmall
            className={css({
              marginBottom: '16px',
            })}
          >
            <div>Coding errors of following queries need to be fixed before installing them:</div>
            <ul>
              {queriesHasError.map((query) => (
                <li key={query.queryName} className={css({ fontWeight: 600, marginLeft: '16px' })}>
                  {query.queryName}
                </li>
              ))}
            </ul>
          </ParagraphSmall>
        )}

        {queriesToInstall.length === 0 && (
          <ParagraphSmall>
            <div>There are no queries to install.</div>
          </ParagraphSmall>
        )}
        {queriesToInstall.length > 0 && (
          <div className={css({ ...theme.typography.ParagraphSmall })}>
            Following queries will be installed:
            <ul>
              {queriesToInstall.map((query) => (
                <li key={query.queryName} className={css({ fontWeight: 600, marginLeft: '16px' })}>
                  {query.queryName}
                </li>
              ))}
            </ul>
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <ModalButton kind="secondary" onClick={onClose}>
          Cancel
        </ModalButton>
        <ModalButton disabled={!queriesToInstall.length} onClick={onConfirm}>
          Install
        </ModalButton>
      </ModalFooter>
    </Modal>
  );
}
