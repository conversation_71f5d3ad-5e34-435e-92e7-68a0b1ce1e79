import { useEffect, useState, useCallback } from 'react';
import { GsqlQueryMeta } from '@tigergraph/tools-models';
import { codeCheckReq, CodeCheckRequest } from '@/pages/editor/query/codeCheck';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { InstallAllConfirmModal, QueryWithCodeCheck } from '@/pages/editor/query/InstallAllConfirmModal';
import { isDistributedQuery } from '@/pages/editor/query/util';

const MAX_CONCURRENT_REQUESTS = 10;

export interface InstallAllProps {
  graphName: string;
  queries: GsqlQueryMeta[];
  isOpen: boolean;
  onClose: () => void;
}

export function InstallAll({ graphName, queries, isOpen, onClose }: InstallAllProps) {
  const [queriesWithCodeCheck, setQueriesWithCodeCheck] = useState<QueryWithCodeCheck[]>([]);
  const [isCodeChecking, setIsCodeChecking] = useState(false);
  const { currentWorkspace } = useWorkspaceContext();
  const { runCmd } = useEditorContext();

  const handleInstallAll = useCallback(async (queryList: GsqlQueryMeta[]) => {
    if (!currentWorkspace || queryList.length === 0) return;

    setIsCodeChecking(true);
    const queriesWithCodeCheck: QueryWithCodeCheck[] = [];

    // Process queries in batches to limit concurrent requests
    const processQueries = async () => {
      const batchPromises = [];

      for (const query of queryList) {
        const codeCheckRequest: CodeCheckRequest = {
          code: query.code,
          graph: graphName,
        };

        const checkPromise = (async () => {
          try {
            const codeCheckResult = await codeCheckReq(codeCheckRequest, {
              baseURL: `https://${currentWorkspace.nginx_host}`,
              version: currentWorkspace.tg_version,
            });

            const hasErrors = !!codeCheckResult.results?.errors?.length;

            return {
              ...query,
              hasErrors,
              codeCheckCompleted: true,
            };
          } catch (error) {
            console.error(`Code check failed for query ${query.name}:`, error);
            return {
              ...query,
              hasErrors: true, // Assume error if code check fails
              codeCheckCompleted: false,
            };
          }
        })();

        batchPromises.push(checkPromise);

        // Wait for batch to complete if we've reached max concurrent requests
        if (batchPromises.length >= MAX_CONCURRENT_REQUESTS) {
          const results = await Promise.all(batchPromises);
          queriesWithCodeCheck.push(...results);
          batchPromises.length = 0; // Clear the array
        }
      }

      // Process any remaining promises
      if (batchPromises.length > 0) {
        const results = await Promise.all(batchPromises);
        queriesWithCodeCheck.push(...results);
      }
    };

    await processQueries();
    setQueriesWithCodeCheck(queriesWithCodeCheck);
    setIsCodeChecking(false);
  }, [currentWorkspace, graphName]);

  const handleConfirmInstall = (selectedQueries: QueryWithCodeCheck[]) => {
    if (selectedQueries.length === 0) return;

    const distributedQueryNames: string[] = [];
    const singleQueryNames: string[] = [];
    selectedQueries.forEach((queryMeta) => {
      if (isDistributedQuery(queryMeta.code)) {
        distributedQueryNames.push(queryMeta.name);
      } else {
        singleQueryNames.push(queryMeta.name);
      }
    }, []);
    let cmdText = `USE GRAPH ${graphName}`;
    // Install distributed queries
    if (distributedQueryNames.length > 0) {
      cmdText += `\nINSTALL QUERY ${distributedQueryNames.join(',')}`;
    }
    // Install single queries to improve performance and enable query profiling
    if (singleQueryNames.length > 0) {
      cmdText += `\nINSTALL QUERY -SINGLE ${singleQueryNames.join(',')}`;
    }

    runCmd(currentWorkspace!, graphName, cmdText);
    onClose();
  };

  // start code check when the modal is opened
  useEffect(() => {
    if (isOpen) {
      handleInstallAll(queries);
    }
  }, [handleInstallAll, isOpen, queries]);

  return (
    <InstallAllConfirmModal
      showModal={isOpen}
      queries={queriesWithCodeCheck}
      isCodeChecking={isCodeChecking}
      onClose={onClose}
      onConfirm={handleConfirmInstall}
      graphName={graphName}
    />
  );
}
