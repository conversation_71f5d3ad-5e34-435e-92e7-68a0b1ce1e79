import { useEffect, useState, useCallback, useRef } from 'react';
import { GsqlQueryMeta } from '@tigergraph/tools-models';
import { codeCheckReq, CodeCheckRequest } from '@/pages/editor/query/codeCheck';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { useEditorContext } from '@/contexts/graphEditorContext';
import { InstallAllConfirmModal, QueryWithCodeCheck } from '@/pages/editor/query/InstallAllConfirmModal';
import { isDistributedQuery } from '@/pages/editor/query/util';
import { showToast } from '@tigergraph/app-ui-lib/styledToasterContainer';
import { getErrorMessage } from '@/utils/utils';

const MAX_CONCURRENT_REQUESTS = 10;

export interface InstallAllProps {
  graphName: string;
  queries: GsqlQueryMeta[];
}

export function InstallAll({ graphName, queries }: InstallAllProps) {
  const [queriesWithCodeCheck, setQueriesWithCodeCheck] = useState<QueryWithCodeCheck[]>([]);
  const [isCodeChecking, setIsCodeChecking] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { currentWorkspace } = useWorkspaceContext();
  const { runCmd } = useEditorContext();

  const handleInstallAll = useCallback(
    async (queryList: GsqlQueryMeta[]) => {
      if (!queryList.length) return;

      setIsCodeChecking(true);
      const queriesWithCodeCheck: QueryWithCodeCheck[] = [];

      // Process queries in batches to limit concurrent requests
      const processQueries = async () => {
        const batchPromises = [];

        for (const query of queryList) {
          const codeCheckRequest: CodeCheckRequest = {
            code: query.code,
            graph: graphName,
          };

          const checkPromise = (async () => {
            try {
              const codeCheckResult = await codeCheckReq(codeCheckRequest, {
                baseURL: `https://${currentWorkspace?.nginx_host}`,
                version: currentWorkspace?.tg_version,
              });

              const hasErrors = !!codeCheckResult.results?.errors?.length;

              return {
                ...query,
                hasErrors,
                codeCheckCompleted: true,
              };
            } catch (error) {
              console.error(`Code check failed for query ${query.name}:`, error);
              return {
                ...query,
                hasErrors: true, // Assume error if code check fails
                codeCheckCompleted: false,
              };
            }
          })();

          batchPromises.push(checkPromise);

          // Wait for batch to complete if we've reached max concurrent requests
          if (batchPromises.length >= MAX_CONCURRENT_REQUESTS) {
            const results = await Promise.all(batchPromises);
            queriesWithCodeCheck.push(...results);
            batchPromises.length = 0; // Clear the array
          }
        }

        // Process any remaining promises
        if (batchPromises.length > 0) {
          const results = await Promise.all(batchPromises);
          queriesWithCodeCheck.push(...results);
        }
      };

      try {
        await processQueries();
        setQueriesWithCodeCheck(queriesWithCodeCheck);
        setShowConfirmDialog(true);
      } catch (error) {
        showToast({
          kind: 'negative',
          message: `Failed to check code errors: ${getErrorMessage(error as Error)}`,
        });
      } finally {
        setIsCodeChecking(false);
      }
    },
    [currentWorkspace?.nginx_host, currentWorkspace?.tg_version, graphName]
  );

  const handleConfirmInstall = (selectedQueries: QueryWithCodeCheck[]) => {
    if (selectedQueries.length === 0) return;

    const distributedQueryNames: string[] = [];
    const singleQueryNames: string[] = [];
    selectedQueries.forEach((queryMeta) => {
      if (isDistributedQuery(queryMeta.code)) {
        distributedQueryNames.push(queryMeta.name);
      } else {
        singleQueryNames.push(queryMeta.name);
      }
    }, []);
    let cmdText = `USE GRAPH ${graphName}`;
    // Install distributed queries
    if (distributedQueryNames.length > 0) {
      cmdText += `\nINSTALL QUERY ${distributedQueryNames.join(',')}`;
    }
    // Install single queries to improve performance and enable query profiling
    if (singleQueryNames.length > 0) {
      cmdText += `\nINSTALL QUERY -SINGLE ${singleQueryNames.join(',')}`;
    }

    runCmd(currentWorkspace!, graphName, cmdText);
    setShowConfirmDialog(false);
  };

  return (
    <InstallAllConfirmModal
      showModal={showConfirmDialog}
      queries={queriesWithCodeCheck}
      onClose={() => setShowConfirmDialog(false)}
      onConfirm={handleConfirmInstall}
      graphName={graphName}
    />
  );
}
