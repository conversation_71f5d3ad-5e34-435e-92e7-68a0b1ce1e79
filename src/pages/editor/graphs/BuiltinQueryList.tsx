import { useState, useMemo } from 'react';
import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { Expandable, ListItem, ListItemLabel } from '@/components/Expandable';
import { PLACEMENT, TRIGGER_TYPE } from 'baseui/popover';
import StatefulPopover from '@/pages/editor/StatefulPopover';
import { StatefulPopover as StatefulToolTipsPopover } from '@tigergraph/app-ui-lib/popover';
import { useWorkspaceContext } from '@/contexts/workspaceContext';
import { useBuiltinEndpoints } from '@/utils/useBuiltinEndpoints';
// Drawer components removed as they're not needed
import { Button } from '@tigergraph/app-ui-lib/button';
import { APIIcon } from '@/pages/editor/graphs/icons';
import { PlayIcon, TriangleRight } from 'lucide-react';
import StatefulTooltip from '@/components/Tooltip';
import { LoadingIndicator } from '@/components/loading-indicator';

// Define the built-in query API paths that we want to display
const builtinQueryAPIPaths = {
  'DELETE /graph/delete_by_type/vertices/{vertex_type}': 'delete_vertex_by_type',
  'DELETE /graph/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}': 'delete_edge',
  'DELETE /graph/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}': 'delete_edge_with_discriminator',
  'DELETE /graph/vertices/{vertex_type}/{vertex_id}': 'delete_vertex',
  'DELETE /graph/{graph_name}/delete_by_type/vertices/{vertex_type}/': 'delete_vertex_by_type_on_graph',
  'DELETE /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}': 'delete_edge_by_type_on_graph',
  'DELETE /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}': 'delete_edge_with_discriminator_on_graph',
  'DELETE /graph/{graph_name}/vertices/{vertex_type}/{vertex_id}': 'delete_vertex_on_graph',
  'GET /graph/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}': 'get_edge',
  'GET /graph/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}': 'get_edge_with_discriminator',
  'GET /graph/vertices/{vertex_type}/{vertex_id}': 'get_vertex',
  'GET /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}': 'get_edge_on_graph',
  'GET /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}': 'get_multi_edge',
  'GET /graph/{graph_name}/vertices/{vertex_type}/{vertex_id}': 'get_vertex_on_graph',
  'POST /graph/{graph_name}': 'update_vertices_or_edges',
};

// Define the endpoint type
interface Endpoint {
  path: string;
  name: string;
  parameters: Record<string, any>;
  description: string;
}

interface EndpointPopoverContentProps {
  endpoint: Endpoint;
}

function EndpointPopoverContent({ endpoint }: EndpointPopoverContentProps) {
  const [css, theme] = useStyletron();

  return (
    <div className="flex flex-col gap-2">
      <div className={css({color: theme.colors['text.primary'], ...theme.typography.Label, fontWeight: 700 })}>{endpoint.name}</div>
      <div className={css({color: theme.colors['text.secondary'], ...theme.typography.Label})}>{endpoint.description}</div>
      <div className='flex justify-end'>
        <Button kind="text" shape='square'><PlayIcon size={16} color={theme.colors['button.icon']} /></Button>
      </div>
    </div>
  )
}

// New component for the content of the Expandable
interface BuiltinQueryContentProps {
  searchText: string;
}

function BuiltinQueryContent({ searchText }: BuiltinQueryContentProps) {
  const [css, theme] = useStyletron();
  const { currentWorkspace } = useWorkspaceContext();

  const { data: builtinEndpoints, isLoading } = useBuiltinEndpoints(currentWorkspace, {
    enabled: !!currentWorkspace,
  });

  // Filter and transform endpoints
  const filteredEndpoints = useMemo(() => {
    if (!builtinEndpoints) return [];

    // Extract endpoints that match our predefined paths
    const endpoints = Object.entries(builtinEndpoints)
      .filter(([path]) => path in builtinQueryAPIPaths)
      .map(([path, endpoint]) => ({
        path,
        name: builtinQueryAPIPaths[path as keyof typeof builtinQueryAPIPaths],
        description: endpoint.description,
        parameters: endpoint.parameters,
      }))
      .filter(endpoint =>
        endpoint.name.toLowerCase().includes(searchText.toLowerCase())
      );

    return endpoints;
  }, [builtinEndpoints, searchText]);

  if (isLoading) {
    return (
      <LoadingIndicator />
    );
  }

  return filteredEndpoints.length === 0 ? (
    <div className={css({ padding: '16px', textAlign: 'center', color: theme.colors['text.secondary'] })}>
      No built-in endpoints found.
    </div>
  ) : (
    <>
      {filteredEndpoints.map((endpoint) => (
        <StatefulPopover
          key={endpoint.path}
          content={
            <EndpointPopoverContent
              endpoint={endpoint}
            />
          }
          placement={PLACEMENT.right}
          ignoreBoundary
          animateOutTime={200}
          dismissOnClickOutside
        >
          <ListItem>
            <ListItemLabel
              icon={<APIIcon />}
              label={endpoint.name}
            />
          </ListItem>
        </StatefulPopover>
      ))}
    </>
  );
}

interface BuiltinQueryListProps {
  searchText?: string;
}

export default function BuiltinQueryList({searchText = ''}: BuiltinQueryListProps) {
  // Header content with icon and label
  const headerContent = (
    <div className="flex items-center justify-between grow">
      <ListItemLabel icon={<APIIcon />} label={'Built-in Queries'} />
    </div>
  );

  return (
    <Expandable
      label={headerContent}
    >
      <BuiltinQueryContent searchText={searchText} />
    </Expandable>
  );
}
